# API Communication Flow - Concise Guide

## Authentication Flow (API-to-API)

### 1. OAuth Initiation

```
Frontend → Zact Backend → QuickBooks OAuth → Frontend
```

**API Call:**

```http
GET /api/connect/qbo
Headers: zactcompanyid: "company-123"

Response:
{
  "data": {
    "authUri": "https://appcenter.intuit.com/connect/oauth2?..."
  }
}
```

### 2. OAuth Callback & Token Exchange

```
QuickBooks → Zact Backend → QuickBooks Token API → Database → Frontend Redirect
```

**API Calls:**

```http
# 1. QBO calls our callback
GET /api/qbo/callback?code=AUTH_CODE&realmId=COMPANY_ID&state=company-123

# 2. Backend exchanges code for tokens
POST https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer
Body: {
  "grant_type": "authorization_code",
  "code": "AUTH_CODE",
  "redirect_uri": "http://localhost:8080/api/qbo/callback"
}

Response: {
  "access_token": "ACCESS_TOKEN",
  "refresh_token": "REFRESH_TOKEN",
  "expires_in": 3600
}

# 3. Backend stores tokens and redirects frontend
Redirect: http://localhost:4200?erpSystem=QBO&realmId=COMPANY_ID
```

## Three Key Messages After Authentication

### Message 1: Connection Success (Immediate)

**Sent to Frontend via Backend redirect:**

```
Backend Response: HTTP 302 Redirect
Location: http://localhost:4200?erpSystem=QBO&realmId=COMPANY_ID
```

### Message 2: Initial Sync Started (Kafka)

**Sent to Kafka topic: `entity-batch-stream`**

```json
{
  "messageId": "initial-sync-123",
  "correlationId": "sync-456",
  "messageType": "EVENT",
  "erpSystem": "QBO",
  "entityOperation": {
    "entityType": "ALL",
    "operation": "SYNC"
  },
  "payload": {
    "message": "Initial sync started for all entities",
    "entities": ["ACCOUNT", "VENDOR", "CLASS"],
    "syncType": "INITIAL",
    "estimatedTime": "5-10 minutes",
    "status": "INITIATED"
  },
  "securityContext": {
    "connectionId": "conn-123"
  }
}
```

### Message 3: Sync Completion (Kafka)

**Sent to Kafka topic: `sync-completion-response`**

```json
{
  "messageId": "sync-complete-123",
  "correlationId": "sync-456",
  "messageType": "RESPONSE",
  "entityOperation": {
    "entityType": "ALL",
    "operation": "SYNC"
  },
  "payload": {
    "message": "Initial sync completed successfully",
    "entityCounts": {
      "vendor": 25,
      "account": 50,
      "class": 10
    },
    "totalEntities": 85,
    "processingTimeMs": 45000,
    "syncType": "INITIAL"
  },
  "status": {
    "code": "SYNC_COMPLETE",
    "message": "Initial synchronization completed successfully"
  },
  "securityContext": {
    "connectionId": "conn-123"
  }
}
```

## API-to-API Communication During Sync

### 1. Fetch Data from QuickBooks

```
Zact Backend → API Gateway → QuickBooks API
```

**API Call:**

```http
GET /api-gateway?service=qbo&entity=vendor&accessToken=ACCESS_TOKEN&companyId=*********

Response:
{
  "QueryResponse": {
    "Vendor": [
      {
        "Id": "1",
        "Name": "Vendor A",
        "Active": true,
        "PrimaryEmailAddr": {"Address": "<EMAIL>"}
      }
    ]
  }
}
```

### 2. Transform & Save

```
API Gateway → Zact Backend → Database → Kafka
```

**Process:**

1. Map QBO format to unified format
2. Save to database
3. Send completion message to Kafka

## Token Refresh Flow (API-to-API)

When access token expires:

```
Zact Backend → QuickBooks Token API → Database → Retry Original Call
```

**API Call:**

```http
POST https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer
Body: {
  "grant_type": "refresh_token",
  "refresh_token": "REFRESH_TOKEN"
}

Response: {
  "access_token": "NEW_ACCESS_TOKEN",
  "refresh_token": "NEW_REFRESH_TOKEN",
  "expires_in": 3600
}
```

## Complete Flow Summary

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant BE as Zact Backend
    participant QBO as QuickBooks API
    participant Kafka as Kafka

    Note over FE,Kafka: Authentication
    FE->>BE: GET /api/connect/qbo
    BE->>FE: OAuth URL
    FE->>QBO: User authorizes
    QBO->>BE: Callback with code
    BE->>QBO: Exchange for tokens
    BE->>BE: Store tokens in database

    Note over FE,Kafka: Three Messages
    BE->>FE: Message 1: Redirect "Connected successfully"
    BE->>Kafka: Message 2: "Initial sync started"

    Note over FE,Kafka: Background Sync
    BE->>QBO: Fetch entities
    QBO->>BE: Entity data
    BE->>Kafka: Message 3: "Initial sync completed"
```

## Key Points

1. **Authentication**: OAuth 2.0 with token exchange
2. **Message 1**: Immediate success response to frontend
3. **Message 2**: Kafka message indicating background sync started
4. **API Communication**: Via API Gateway for external calls
5. **Token Management**: Automatic refresh when expired
6. **Data Flow**: QBO → Transform → Database → Kafka notification
