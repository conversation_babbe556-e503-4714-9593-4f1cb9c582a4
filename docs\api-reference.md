# Zact Unified Backend - API Reference

## Base URL
```
http://localhost:8080/api
```

## Authentication

All API endpoints require proper authentication headers. The system uses OAuth 2.0 for QuickBooks Online integration.

### Required Headers
- `zactcompanyid`: Your organization/company identifier
- `Content-Type`: `application/json` (for POST requests)

## Authentication Endpoints

### 1. Initiate OAuth Connection

Starts the OAuth flow for connecting to an accounting platform.

```http
GET /connect/:accountingPlatform
```

**Parameters:**
- `accountingPlatform` (path): Currently supports `qbo` (QuickBooks Online)

**Headers:**
- `zactcompanyid` (required): Organization identifier

**Response:**
```json
{
  "responseStatus": 200,
  "message": "QBO authorization URL generated successfully",
  "data": {
    "authUri": "https://appcenter.intuit.com/connect/oauth2?client_id=..."
  }
}
```

**Example:**
```bash
curl -X GET "http://localhost:8080/api/connect/qbo" \
  -H "zactcompanyid: your-company-id"
```

### 2. OAuth Callback

Handles the OAuth callback from the accounting platform.

```http
GET /:accountingPlatform/callback
```

**Parameters:**
- `accountingPlatform` (path): The accounting platform (`qbo`)
- `code` (query): Authorization code from OAuth provider
- `realmId` (query): Company ID from OAuth provider
- `state` (query): State parameter (contains zactcompanyid)

**Response:**
Redirects to frontend application with success parameters.

### 3. Disconnect Integration

Disconnects the current accounting platform integration.

```http
POST /disconnect
```

**Headers:**
- `zactcompanyid` (required): Organization identifier

**Response:**
```json
{
  "responseStatus": 200,
  "message": "Successfully disconnected from QBO",
  "data": {
    "connectionStatus": "DISCONNECTED",
    "disconnectedAt": "2024-01-15T10:30:00Z"
  }
}
```

## Service Endpoints

### 1. Generic Service Call

Performs generic operations on accounting platform entities.

```http
GET /service
```

**Query Parameters:**
- `entity` (required): Entity type (`vendor`, `bill`, `account`, etc.)
- `connectionId` (required): Integration connection ID
- `operation` (required): Operation type (`fetch` or `post`)

**Response:**
```json
{
  "responseStatus": 200,
  "message": "Success",
  "data": {
    // Entity-specific data
  }
}
```

**Example:**
```bash
curl -X GET "http://localhost:8080/api/service?entity=vendor&connectionId=conn-123&operation=fetch"
```

### 2. Instant Sync

Performs instant synchronization for specific entity types.

```http
POST /sync/instant/:entityType
```

**Parameters:**
- `entityType` (path): Entity to sync (`ACCOUNT`, `VENDOR`, `CLASS`, `ALL`)

**Headers:**
- `zactcompanyid` (required): Organization identifier

**Query Parameters:**
- `batchSize` (optional): Number of records per batch (default: 1000)

**Response:**
```json
{
  "data": {
    "create": {
      "vendor": [
        {
          "id": "vendor-123",
          "name": "Acme Corp",
          "email": "<EMAIL>",
          "isActive": true
        }
      ],
      "account": [
        {
          "id": "account-456",
          "name": "Office Supplies",
          "accountType": "EXPENSE"
        }
      ]
    },
    "update": {
      "vendor": [],
      "account": []
    }
  },
  "message": "Instant sync completed for ALL entities: 10 created, 5 updated across 2 entity types",
  "summary": {
    "created": 10,
    "updated": 5
  },
  "details": {
    "successful": [
      {
        "entityType": "VENDOR",
        "summary": { "created": 5, "updated": 2 }
      }
    ],
    "failed": []
  }
}
```

**Example:**
```bash
curl -X POST "http://localhost:8080/api/sync/instant/VENDOR?batchSize=500" \
  -H "zactcompanyid: your-company-id"
```

### 3. Manual Cron Trigger

Manually triggers the scheduled sync cron job for testing.

```http
POST /sync/cron/trigger
```

**Response:**
```json
{
  "responseStatus": 200,
  "message": "Cron job triggered successfully",
  "data": {
    "status": "running",
    "triggeredAt": "2024-01-15T10:30:00Z"
  }
}
```

## Error Responses

All endpoints return standardized error responses:

```json
{
  "responseStatus": 400,
  "error": {
    "status": 400,
    "code": "BAD_REQUEST",
    "errorDescription": "Detailed error description"
  },
  "message": "User-friendly error message"
}
```

### Common Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `BAD_REQUEST` | 400 | Invalid request parameters |
| `UNAUTHORIZED` | 401 | Authentication required or invalid |
| `FORBIDDEN` | 403 | Access denied |
| `NOT_FOUND` | 404 | Resource not found |
| `UNPROCESSABLE_ENTITY` | 422 | Validation failed |
| `INTERNAL_ERROR` | 500 | Server error |
| `SERVICE_UNAVAILABLE` | 503 | External service unavailable |

## Rate Limiting

The API implements rate limiting to prevent abuse:
- **Rate Limit**: 100 requests per minute per IP
- **Headers**: Rate limit information is included in response headers
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset time

## Monitoring Endpoints

### Health Check
```http
GET /health
```

### Metrics (Prometheus)
```http
GET /metrics
```

## Webhook Endpoints

### Kafka Message Status
The system publishes status updates to Kafka topics:

**Topics:**
- `entity-create-request`: Entity creation requests
- `entity-create-response`: Entity creation responses
- `entity-batch-stream`: Batch synchronization data
- `sync-completion-response`: Sync completion notifications

**Message Format:**
```json
{
  "messageId": "msg-123",
  "correlationId": "corr-456",
  "timestamp": 1642248600000,
  "source": "UNIFIED_BACKEND",
  "destination": "ZACT_APP",
  "messageType": "RESPONSE",
  "erpSystem": "QBO",
  "entityOperation": {
    "entityType": "vendor",
    "operation": "CREATE"
  },
  "payload": {
    "data": { /* entity data */ },
    "message": "Entity created successfully"
  },
  "status": {
    "code": "SUCCESS",
    "message": "Operation completed"
  },
  "securityContext": {
    "connectionId": "conn-123"
  }
}
```

## SDK Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

const client = axios.create({
  baseURL: 'http://localhost:8080/api',
  headers: {
    'zactcompanyid': 'your-company-id'
  }
});

// Initiate OAuth
const authResponse = await client.get('/connect/qbo');
console.log('Auth URL:', authResponse.data.data.authUri);

// Perform instant sync
const syncResponse = await client.post('/sync/instant/VENDOR');
console.log('Sync result:', syncResponse.data);
```

### Python
```python
import requests

base_url = 'http://localhost:8080/api'
headers = {'zactcompanyid': 'your-company-id'}

# Initiate OAuth
auth_response = requests.get(f'{base_url}/connect/qbo', headers=headers)
auth_url = auth_response.json()['data']['authUri']

# Perform instant sync
sync_response = requests.post(f'{base_url}/sync/instant/VENDOR', headers=headers)
sync_result = sync_response.json()
```

This API reference provides comprehensive documentation for all available endpoints in the Zact Unified Backend system.
