# Zact Unified Backend Documentation

Welcome to the comprehensive documentation for the Zact Unified Backend system. This documentation covers authentication flows, API patterns, system architecture, and integration guides.

## 📚 Documentation Index

### 1. [Authentication & API Flow](./authentication-and-api-flow.md)
**Complete guide to authentication and API request/response patterns**

- OAuth 2.0 authentication flow with QuickBooks Online
- Token management and automatic refresh mechanisms
- API request/response lifecycle
- Middleware chain processing
- Error handling patterns
- Security features and best practices

**Key Topics:**
- OAuth initiation and callback handling
- Token validation and refresh with race condition prevention
- API Gateway integration patterns
- Comprehensive error classification and handling
- Request logging and monitoring

### 2. [System Architecture Diagrams](./system-architecture-diagrams.md)
**Visual representations of system components and data flows**

- High-level system architecture overview
- Detailed authentication flow diagrams
- Entity creation flow (Kafka-based)
- Middleware processing chain
- Token management architecture
- Database schema relationships
- Error handling flow
- Kafka message flow architecture

**Key Diagrams:**
- Sequence diagrams for OAuth and API flows
- Flowcharts for middleware and error processing
- Entity relationship diagrams for database schema
- Kafka topic and message flow visualizations

### 3. [API Reference](./api-reference.md)
**Complete API endpoint documentation with examples**

- Authentication endpoints (OAuth initiation, callback, disconnect)
- Service endpoints (generic calls, instant sync, cron triggers)
- Request/response formats and examples
- Error codes and handling
- Rate limiting information
- SDK examples in multiple languages

**Key Features:**
- Detailed endpoint specifications
- Request/response examples
- Error response formats
- Rate limiting guidelines
- SDK usage examples

### 4. [Kafka Messaging Guide](./kafka-messaging-guide.md)
**Comprehensive guide to Kafka integration and messaging patterns**

- Kafka topics and their purposes
- Message structure and interfaces
- Request-response patterns
- Batch processing workflows
- Error handling in messages
- Consumer configuration
- Best practices for message design

**Key Topics:**
- Message format specifications
- Entity creation and sync workflows
- Batch processing for large datasets
- Error handling and retry mechanisms
- Performance optimization guidelines

## 🚀 Quick Start Guide

### Prerequisites
- Node.js 18+ and npm
- MySQL database
- Kafka cluster (local or remote)
- QuickBooks Online developer account

### Environment Setup

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd Zact_Unified_backend
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Database Setup**
   ```bash
   npx prisma migrate dev
   npx prisma generate
   ```

4. **Start Services**
   ```bash
   # Start Kafka (if running locally)
   # Start MySQL database
   
   # Start the application
   npm run dev
   ```

### Basic Usage Flow

1. **Initiate OAuth Connection**
   ```bash
   curl -X GET "http://localhost:8080/api/connect/qbo" \
     -H "zactcompanyid: your-company-id"
   ```

2. **Complete OAuth Flow**
   - Redirect user to returned `authUri`
   - User authorizes application
   - System handles callback automatically

3. **Perform Operations**
   ```bash
   # Instant sync
   curl -X POST "http://localhost:8080/api/sync/instant/VENDOR" \
     -H "zactcompanyid: your-company-id"
   ```

## 🏗️ System Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │  Zact Backend   │    │ External APIs   │
│                 │    │                 │    │                 │
│ • Frontend      │◄──►│ • Express API   │◄──►│ • QuickBooks    │
│ • Mobile        │    │ • Auth Service  │    │ • Other ERPs    │
│ • Third-party   │    │ • Kafka System  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ Infrastructure  │
                       │                 │
                       │ • MySQL DB      │
                       │ • Kafka Cluster │
                       │ • Monitoring    │
                       └─────────────────┘
```

## 🔐 Security Features

- **OAuth 2.0 Authentication**: Secure token-based authentication
- **Token Management**: Automatic refresh with race condition prevention
- **Request Validation**: Comprehensive input validation and sanitization
- **Error Sanitization**: No sensitive data exposed in error responses
- **Audit Logging**: Complete request and response logging
- **Rate Limiting**: Protection against abuse and DoS attacks

## 📊 Monitoring & Observability

- **Prometheus Metrics**: Request duration, counts, and custom metrics
- **Database Logging**: All API requests and Kafka messages logged
- **Error Tracking**: Comprehensive error logging and classification
- **Health Checks**: System health monitoring endpoints
- **Request Tracing**: Correlation IDs for request tracking

## 🔄 Data Flow Patterns

### 1. Synchronous API Calls
```
Client → Express → Auth → Service → External API → Database → Response
```

### 2. Asynchronous Kafka Processing
```
Client → Kafka Topic → Consumer → Validation → Mapping → API → Database → Response Topic
```

### 3. Batch Synchronization
```
Scheduler → Sync Service → Batch Messages → Consumer → Database → Completion Message
```

## 🛠️ Development Guidelines

### Code Organization
- **Services**: Business logic and external integrations
- **Routes**: API endpoint definitions
- **Middleware**: Request processing and validation
- **Utils**: Helper functions and utilities
- **Kafka**: Message producers and consumers

### Error Handling
- Use `ApiException` for all business logic errors
- Implement proper error logging and monitoring
- Return sanitized error responses to clients
- Use correlation IDs for error tracing

### Testing
- Unit tests for business logic
- Integration tests for API endpoints
- Kafka message testing
- Database operation testing

## 📈 Performance Considerations

- **Connection Pooling**: Database and HTTP connections
- **Batch Processing**: Large dataset handling
- **Caching**: Token caching and validation
- **Rate Limiting**: API protection
- **Message Batching**: Kafka optimization

## 🔧 Configuration Management

All configuration is managed through environment variables:
- Server settings (port, environment)
- Database connection strings
- OAuth credentials
- Kafka configuration
- API Gateway settings
- Monitoring and logging settings

## 📞 Support & Troubleshooting

### Common Issues
1. **Authentication Failures**: Check OAuth credentials and token expiration
2. **Kafka Connection Issues**: Verify Kafka broker connectivity
3. **Database Errors**: Check connection strings and permissions
4. **API Gateway Timeouts**: Review timeout settings and external service status

### Debugging
- Check application logs for detailed error information
- Use correlation IDs to trace requests across components
- Monitor Prometheus metrics for performance insights
- Review database logs for query performance

### Getting Help
- Review this documentation thoroughly
- Check application logs and error messages
- Use monitoring dashboards for system insights
- Contact the development team for complex issues

---

This documentation is maintained alongside the codebase and should be updated when system changes are made.
