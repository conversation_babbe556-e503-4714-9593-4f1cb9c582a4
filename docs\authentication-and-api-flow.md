# Zact Unified Backend - Authentication & API Flow Documentation

## Table of Contents

1. [Overview](#overview)
2. [Authentication Flow](#authentication-flow)
3. [API Request/Response Flow](#api-requestresponse-flow)
4. [Middleware Chain](#middleware-chain)
5. [Error Handling](#error-handling)
6. [Kafka Integration](#kafka-integration)
7. [Database Operations](#database-operations)
8. [Security Features](#security-features)

## Overview

The Zact Unified Backend is a Node.js/Express application that provides a unified interface for accounting platform integrations, primarily QuickBooks Online (QBO). It handles OAuth authentication, API calls, data transformation, and Kafka messaging.

**Key Components:**

- Express.js server with middleware chain
- OAuth 2.0 authentication for QBO
- Token management with automatic refresh
- API Gateway for external service calls
- Kafka producers/consumers for messaging
- Database operations with Prisma ORM
- Comprehensive error handling and logging

## Authentication Flow

### 1. OAuth Initiation Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant Backend as Zact Backend
    participant QBO as <PERSON>Books Online
    participant DB as Database

    Client->>Backend: GET /api/connect/qbo
    Note over Client,Backend: Headers: zactcompanyid

    Backend->>Backend: Validate zactcompanyid header
    Backend->>Backend: Generate OAuth URL with state
    Backend->>Client: Return authUri

    Client->>QBO: Redirect to OAuth URL
    QBO->>Client: User authorizes application
    QBO->>Backend: GET /api/qbo/callback?code&realmId&state

    Backend->>QBO: Exchange code for tokens
    QBO->>Backend: Return access_token, refresh_token

    Backend->>DB: Store/Update integration data
    Backend->>Client: Redirect with success parameters
```

### 2. Token Management Flow

```mermaid
sequenceDiagram
    participant API as API Request
    participant TokenUtils as Token Utils
    participant DB as Database
    participant QBO as QuickBooks Online

    API->>TokenUtils: getValidQboToken(connectionId)
    TokenUtils->>DB: Fetch integration & auth data

    alt Token is valid
        TokenUtils->>API: Return accessToken & realmId
    else Token expired
        TokenUtils->>TokenUtils: Check for existing refresh operation
        alt No refresh in progress
            TokenUtils->>QBO: Refresh token request
            QBO->>TokenUtils: New tokens
            TokenUtils->>DB: Update authentication data
            TokenUtils->>API: Return new accessToken & realmId
        else Refresh in progress
            TokenUtils->>TokenUtils: Wait for existing refresh
            TokenUtils->>API: Return refreshed tokens
        end
    end
```

## API Request/Response Flow

### 1. Standard API Request Flow

```mermaid
sequenceDiagram
    participant Client as Client
    participant Express as Express App
    participant Middleware as Middleware Chain
    participant Handler as Route Handler
    participant Service as Service Layer
    participant External as External API
    participant DB as Database

    Client->>Express: HTTP Request
    Express->>Middleware: Security Headers
    Express->>Middleware: CORS
    Express->>Middleware: Body Parser
    Express->>Middleware: API Logger (Start)
    Express->>Middleware: Async Handler Wrapper

    Middleware->>Handler: Validated Request
    Handler->>Service: Business Logic
    Service->>External: API Gateway Call
    External->>Service: Response Data
    Service->>DB: Save/Update Data
    Service->>Handler: Processed Result

    Handler->>Middleware: Success Response
    Middleware->>Express: JSON Response
    Express->>Middleware: API Logger (End)
    Express->>Client: HTTP Response
```

### 2. Entity Creation Flow (via Kafka)

```mermaid
sequenceDiagram
    participant Client as Client App
    participant Kafka as Kafka Topic
    participant Consumer as Entity Consumer
    participant Validator as Validator
    participant Mapper as Mapper
    participant Gateway as API Gateway
    participant QBO as QuickBooks Online
    participant DB as Database

    Client->>Kafka: Publish to entity-create-request
    Kafka->>Consumer: Consume message

    Consumer->>Validator: Validate unified format
    alt Validation fails
        Consumer->>Kafka: Publish error to entity-create-response
    else Validation passes
        Consumer->>Mapper: Map to QBO format
        Consumer->>Gateway: POST request
        Gateway->>QBO: Create entity
        QBO->>Gateway: QBO response
        Gateway->>Consumer: Mapped response
        Consumer->>DB: Save unified entity
        Consumer->>Kafka: Publish success to entity-create-response
    end
```

## Middleware Chain

The application uses a comprehensive middleware chain for request processing:

### 1. Security Middleware

- **Helmet**: Sets security headers (CSP, HSTS, etc.)
- **Custom Security Headers**: Additional security configurations
- **CORS**: Cross-origin resource sharing

### 2. Monitoring & Logging

- **Prometheus Metrics**: Request duration and counters
- **Request Tracing**: Unique request IDs and correlation
- **API Logger**: Database logging of requests/responses

### 3. Request Processing

- **Body Parser**: JSON/URL-encoded parsing
- **Async Handler**: Wraps route handlers for error handling
- **Validation**: Request validation using express-validator

### 4. Error Handling

- **Global Error Handler**: Centralized error processing
- **Not Found Handler**: 404 error handling

## Error Handling

### 1. Error Classification

```typescript
// API Exception Structure
{
  status: number,           // HTTP status code
  code: string,            // Internal error code
  message: string,         // User-friendly message
  errorDescription: string // Detailed error description
}
```

### 2. Error Flow

```mermaid
flowchart TD
    A[Error Occurs] --> B{Error Type?}
    B -->|ApiException| C[Use existing structure]
    B -->|Validation Error| D[Convert to ApiException]
    B -->|Token Error| E[Handle auth errors]
    B -->|Unknown Error| F[Create generic ApiException]

    C --> G[Global Error Handler]
    D --> G
    E --> G
    F --> G

    G --> H[Log Error]
    G --> I[Format Response]
    G --> J[Send to Client]
```

## Kafka Integration

### 1. Message Flow Architecture

```mermaid
flowchart LR
    A[Client Request] --> B[entity-create-request]
    B --> C[Entity Create Consumer]
    C --> D[Validation & Mapping]
    D --> E[API Gateway Call]
    E --> F[Database Save]
    F --> G[entity-create-response]
    G --> H[Client Response]

    I[Sync Operations] --> J[entity-batch-stream]
    J --> K[Batch Consumer]
    K --> L[sync-completion-response]
```

### 2. Message Structure

```typescript
interface BaseMessage {
  messageId: string;
  correlationId: string;
  timestamp: number;
  source: MessageSource;
  destination: MessageDestination;
  messageType: MessageType;
  erpSystem: ERPSystem;
  entityOperation: EntityOperation;
  securityContext: SecurityContext;
}
```

## Database Operations

### 1. Connection Management

- **Prisma ORM**: Type-safe database operations
- **Connection Pooling**: Automatic connection management
- **Transaction Support**: ACID compliance

### 2. Data Flow

```mermaid
flowchart TD
    A[API Request] --> B[Service Layer]
    B --> C[Repository Pattern]
    C --> D[Prisma Client]
    D --> E[MySQL Database]

    F[Kafka Message] --> G[Consumer]
    G --> H[Entity Repository]
    H --> D

    I[Background Sync] --> J[Sync Service]
    J --> K[Batch Operations]
    K --> D
```

## Security Features

### 1. Authentication Security

- **OAuth 2.0**: Secure token exchange
- **Token Refresh**: Automatic token renewal
- **Connection Locking**: Prevents race conditions
- **Secure Storage**: Encrypted token storage

### 2. Request Security

- **HTTPS Enforcement**: Secure transport
- **CORS Protection**: Cross-origin controls
- **Rate Limiting**: Request throttling
- **Input Validation**: Request sanitization

### 3. Data Security

- **Database Encryption**: Sensitive data protection
- **Audit Logging**: Complete request tracking
- **Error Sanitization**: No sensitive data in errors

## Configuration

### Environment Variables

```bash
# Server Configuration
PORT=8080
NODE_ENV=production

# Database
DATABASE_URL="mysql://user:password@localhost:3306/zact_staging"

# QBO OAuth
QBO_CLIENT_ID=your_client_id
QBO_CLIENT_SECRET=your_client_secret
QBO_REDIRECT_URI=http://localhost:8080/api/qbo/callback

# Kafka
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=zact-unified-backend
KAFKA_CONSUMER_GROUP_ID=zact-unified-backend-group

# API Gateway
API_GATEWAY_BASE_URL=https://api.gateway.url
API_GATEWAY_TIMEOUT=30000
```

## Detailed API Endpoints

### Authentication Endpoints

#### 1. Initiate OAuth Connection

```http
GET /api/connect/:accountingPlatform
Headers:
  zactcompanyid: string (required)

Response:
{
  "responseStatus": 200,
  "message": "QBO authorization URL generated successfully",
  "data": {
    "authUri": "https://appcenter.intuit.com/connect/oauth2?..."
  }
}
```

#### 2. OAuth Callback Handler

```http
GET /api/:accountingPlatform/callback
Query Parameters:
  code: string (required)
  realmId: string (required)
  state: string (required)

Response: Redirect to frontend with success parameters
```

#### 3. Disconnect Integration

```http
POST /api/disconnect
Headers:
  zactcompanyid: string (required)

Response:
{
  "responseStatus": 200,
  "message": "Successfully disconnected from QBO",
  "data": {...}
}
```

### Service Endpoints

#### 1. Generic Service Call

```http
GET /api/service
Query Parameters:
  entity: string (required)
  connectionId: string (required)
  operation: "fetch" | "post" (required)

Response:
{
  "responseStatus": 200,
  "message": "Success",
  "data": {...}
}
```

#### 2. Instant Sync

```http
POST /api/sync/instant/:entityType
Headers:
  zactcompanyid: string (required)
Query Parameters:
  batchSize: number (optional, default: 1000)

Response:
{
  "data": {
    "create": {
      "vendor": [...],
      "account": [...]
    },
    "update": {
      "vendor": [...],
      "account": [...]
    }
  },
  "message": "Instant sync completed...",
  "summary": {
    "created": 10,
    "updated": 5
  }
}
```

## Token Management Details

### Token Validation Process

```typescript
// Token validation with automatic refresh
export const getValidQboToken = async (connectionId: string) => {
  // 1. Fetch integration from database
  const integration = await prisma.accountingPlatformIntegration.findUnique({
    where: { id: connectionId },
  });

  // 2. Check token expiration
  const auth = integration.authentication;
  const expiresAt = new Date(auth.expiresAt);
  const now = new Date();

  // 3. Return valid token or refresh if expired
  if (expiresAt > now) {
    return {
      accessToken: auth.accessToken,
      realmId: integration.externalCompanyId,
    };
  } else {
    return await refreshQboToken(
      connectionId,
      auth,
      integration.externalCompanyId
    );
  }
};
```

### Race Condition Prevention

The system implements a locking mechanism to prevent multiple simultaneous token refresh operations:

```typescript
// Global map to track ongoing refresh operations
const tokenRefreshLocks = new Map<string, Promise<any>>();

// Check if refresh is already in progress
if (tokenRefreshLocks.has(connectionId)) {
  return await tokenRefreshLocks.get(connectionId);
}

// Create new refresh operation
const refreshOperation = refreshQboToken(connectionId, auth, realmId);
tokenRefreshLocks.set(connectionId, refreshOperation);
```

## API Gateway Integration

### Request Structure

```typescript
interface ApiGatewayRequest {
  service: string; // "qbo"
  entity: string; // "vendor", "bill", etc.
  accessToken: string; // Valid QBO access token
  companyId: string; // QBO realm ID
  connectionId: string; // Internal connection ID
  options?: {
    startPosition?: number;
    maxResults?: number;
    totalCount?: boolean;
    dateField?: string;
    startDate?: string;
  };
}
```

### Response Handling

```typescript
// API Gateway response processing
const processApiGatewayResponse = async (
  responseData: any,
  entityType: string,
  connectionId: string
) => {
  const mainResponseKey = Object.keys(responseData)[0];
  const qboData = responseData[mainResponseKey];

  switch (mainResponseKey.toLowerCase()) {
    case "journalentry":
      const unifiedJournalEntry = mapQboJournalEntryToUnified(qboData);
      return await JournalEntryRepository.save(
        unifiedJournalEntry,
        connectionId
      );

    case "vendor":
      const unifiedVendor = mapQboVendorResponseToUnified(qboData);
      return await VendorRepository.save(unifiedVendor, connectionId);

    // ... other entity types
  }
};
```

## Logging and Monitoring

### Request Logging

Every API request is logged to the database with:

- Request details (query, params, IP)
- Response status and body
- Execution time
- Connection ID for tracing

### Kafka Message Logging

All Kafka messages are logged with:

- Message ID and correlation ID
- Topic and operation type
- Processing status
- Full message payload for debugging

### Metrics Collection

Prometheus metrics are collected for:

- HTTP request duration
- Request count by route and status
- Kafka message processing times
- Database operation metrics

This documentation provides a comprehensive overview of the authentication flow and API patterns in the Zact Unified Backend system.
